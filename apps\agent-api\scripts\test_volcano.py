import asyncio
import os
import sys

# 添加项目路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from api.services.llm_proxy_service import get_llm_proxy_service

async def main():
    print("正在测试火山引擎LLM连接...")
    try:
        # 确保你的settings已经加载了.env文件
        from api.settings import settings
        print(f"使用的Endpoint ID: {settings.VOLCANO_LLM_ENDPOINT_ID}")
        print(f"使用的App Key (前几位): {settings.VOLCANO_LLM_APP_KEY[:5]}...")

        llm_service = await get_llm_proxy_service()
        response = await llm_service.generate_text("你好，请说'连接成功'")

        print("--- LLM 服务响应 ---")
        print(response)
        if "连接成功" in response:
            print("\n[✅] 火山引擎LLM连接成功！")
        else:
            print("\n[❌] LLM服务有响应，但内容不符合预期。请检查模型状态。")

    except Exception as e:
        print(f"\n[❌] 连接测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
