"""
火山引擎RTC客户端服务
提供对火山引擎StartVoiceChat和StopVoiceChat API的封装调用
"""
import asyncio
import datetime
import hashlib
import hmac
import json
import time
from typing import Dict, Any, Optional, List
from urllib.parse import quote, urlencode
import httpx
from api.settings import settings, logger
from api.models.rtc_models import (
    VolcanoAPIError,
    VolcanoStartVoiceChatRequest,
    VoiceChatInteractionConfig,
    ASRConfig, ASRProviderParamsSmallModel, ASRProviderParamsBigModel,
    TTSConfig, TTSProviderParams, TTSProviderParamsApp, TTSProviderParamsAudio,
    LLMConfig,
    AgentConfig,
    FunctionCallingConfig
)
import os
import json
import hashlib
import hmac
import urllib.parse
from datetime import datetime
from typing import Dict, Any, Optional
import aiohttp
import asyncio
import os
from api.settings import logger, settings


class ConfigurationValidator:
    """火山引擎配置验证器 - 确保所有必需的配置都正确设置"""

    @staticmethod
    def validate_environment_variables() -> Dict[str, Any]:
        """验证环境变量配置的完整性"""
        validation_result = {
            "valid": True,
            "missing_configs": [],
            "invalid_configs": [],
            "warnings": []
        }

        # 必需的环境变量配置 - 使用项目实际的变量名
        required_configs = {
            "VOLCANO_ACCESS_KEY_ID": "火山引擎Access Key ID",
            "VOLCANO_SECRET_ACCESS_KEY": "火山引擎Secret Access Key",
            "VOLCANO_RTC_APP_ID": "火山引擎RTC应用ID",
            "VOLCENGINE_WEBHOOK_SECRET": "RTC Webhook验证密钥"
        }

        # 检查必需配置
        for env_var, description in required_configs.items():
            value = os.getenv(env_var)
            if not value:
                validation_result["missing_configs"].append({
                    "variable": env_var,
                    "description": description
                })
                validation_result["valid"] = False
            elif env_var.endswith("_KEY") and len(value) < 10:
                validation_result["invalid_configs"].append({
                    "variable": env_var,
                    "issue": "密钥长度过短，可能无效"
                })
                validation_result["valid"] = False

        # 可选但推荐的配置
        optional_configs = {
            "API_BASE_URL": "API基础URL",
            "VOLCENGINE_ENDPOINT": "火山引擎API端点"
        }

        for env_var, description in optional_configs.items():
            if not os.getenv(env_var):
                validation_result["warnings"].append({
                    "variable": env_var,
                    "description": description,
                    "suggestion": "建议设置以确保服务正常运行"
                })

        return validation_result

    @staticmethod
    def validate_rtc_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """验证RTC配置的完整性和正确性"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }

        # 验证ASR配置
        if "ASRConfig" in config:
            asr_config = config["ASRConfig"]
            if asr_config.get("Provider") == "volcano":
                params = asr_config.get("ProviderParams", {})
                if params.get("Mode") == "bigmodel":
                    required_fields = ["AppId", "AccessToken"]
                    for field in required_fields:
                        if not params.get(field):
                            validation_result["errors"].append(f"ASR配置缺少必需字段: {field}")
                            validation_result["valid"] = False

        # 验证TTS配置
        if "TTSConfig" in config:
            tts_config = config["TTSConfig"]
            if tts_config.get("Provider") == "volcano":
                params = tts_config.get("ProviderParams", {})
                app_config = params.get("app", {})
                if not app_config.get("appid") or not app_config.get("cluster"):
                    validation_result["errors"].append("TTS配置缺少必需的appid或cluster")
                    validation_result["valid"] = False

        # 验证LLM配置
        if "LLMConfig" in config:
            llm_config = config["LLMConfig"]
            mode = llm_config.get("Mode")
            if mode == "ArkV3":
                if not llm_config.get("EndPointId") and not llm_config.get("BotId"):
                    validation_result["errors"].append("LLM配置：ArkV3模式需要EndPointId或BotId")
                    validation_result["valid"] = False
            elif mode == "CustomLLM":
                if not llm_config.get("URL"):
                    validation_result["errors"].append("LLM配置：CustomLLM模式需要URL")
                    validation_result["valid"] = False

        return validation_result


class VolcanoClientService:
    """火山引擎RTC客户端服务"""

    def __init__(self):
        # 验证配置完整性
        validation_result = ConfigurationValidator.validate_environment_variables()

        if not validation_result["valid"]:
            error_message = "火山引擎配置验证失败："
            for missing in validation_result["missing_configs"]:
                error_message += f"\n- 缺少配置: {missing['variable']} ({missing['description']})"
            for invalid in validation_result["invalid_configs"]:
                error_message += f"\n- 无效配置: {invalid['variable']} - {invalid['issue']}"
            logger.error(error_message)

            # 修复：在测试环境中只警告，不抛出异常
            if os.getenv("ENVIRONMENT") == "test":
                logger.warning("测试环境：配置验证失败但继续执行")
            else:
                raise ValueError(error_message)

        self.base_url = "https://rtc.volcengineapi.com"
        self.access_key = settings.VOLCANO_ACCESS_KEY_ID
        self.secret_key = settings.VOLCANO_SECRET_ACCESS_KEY
        self.app_id = settings.VOLCANO_RTC_APP_ID
        self.timeout = 30.0  # 30秒超时
        self.max_retries = 3  # 最大重试3次

        # 记录警告信息
        if validation_result["warnings"]:
            for warning in validation_result["warnings"]:
                logger.warning(f"配置建议: {warning['variable']} - {warning['suggestion']}")

        logger.info("VolcanoClientService 初始化完成，配置验证通过")

    def _validate_llm_config_mutual_exclusion(self, llm_config_data: Dict[str, Any]) -> None:
        """
        验证BotId与EndPointId互斥性（架构师风险点）

        Args:
            llm_config_data: LLMConfig数据字典

        Raises:
            ValueError: 当BotId与EndPointId同时存在时
        """
        has_bot_id = "BotId" in llm_config_data
        has_endpoint_id = "EndPointId" in llm_config_data

        if has_bot_id and has_endpoint_id:
            raise ValueError("BotId与EndPointId不能同时存在")

    def _build_llm_config_data(self, character_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        构建LLMConfig数据，支持第三方LLM集成（符合官方文档）

        Args:
            character_config: 角色配置，应包含可信的配置信息

        Returns:
            LLMConfig数据字典
        """
        # 基础LLM配置
        llm_config_data = {
            "MaxTokens": character_config.get("llm", {}).get("max_tokens", 2048) if character_config else 2048,
            "Temperature": character_config.get("llm", {}).get("temperature", 0.7) if character_config else 0.7,
            "SystemMessages": [character_config.get("personality", {}).get("description", "你是一个乐于助人、善解人意的朋友。")] if character_config else ["你是一个乐于助人、善解人意的朋友。"],
            "Prefill": character_config.get("llm", {}).get("prefill", False) if character_config else False,
        }

        # 获取LLM配置
        llm_settings = character_config.get("llm", {}) if character_config else {}

        # 检查是否配置了第三方LLM（根据官方文档：接入第三方大模型或Agent）
        custom_llm_url = llm_settings.get("custom_url")
        if custom_llm_url:
            logger.info(f"使用第三方LLM配置: {custom_llm_url}")
            llm_config_data.update({
                "Mode": "CustomLLM",  # 官方文档要求的固定值
                "URL": custom_llm_url,  # 第三方LLM的完整HTTPS URL
                "ModelName": llm_settings.get("model_name", "custom_model"),
                "APIKey": llm_settings.get("api_key"),  # Bearer Token认证
            })

            # 添加自定义参数支持（官方文档提到的custom字段）
            custom_params = llm_settings.get("custom", {})
            if custom_params:
                llm_config_data["custom"] = custom_params

        else:
            # 使用火山引擎内部模型
            llm_config_data["Mode"] = "ArkV3"

            # 配置来源安全验证：只从可信的character_config.llm获取bot_id
            bot_id = llm_settings.get("bot_id")

            # BotId与EndPointId互斥逻辑：优先使用bot_id，fallback到endpoint_id
            if bot_id:
                logger.info(f"使用BotId配置: {bot_id}")
                llm_config_data["BotId"] = bot_id
            else:
                logger.info(f"使用EndPointId配置（fallback）: {settings.VOLCANO_LLM_ENDPOINT_ID}")
                llm_config_data["EndPointId"] = settings.VOLCANO_LLM_ENDPOINT_ID

        # 验证配置互斥性（仅适用于火山内部模型）
        if llm_config_data.get("Mode") != "CustomLLM":
            self._validate_llm_config_mutual_exclusion(llm_config_data)

        return llm_config_data

    def _validate_function_calling_tools(self, tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        验证Function Calling工具定义格式，严格符合官方文档要求

        官方文档要求的工具格式：
        {
            "Type": "function",
            "function": {
                "name": "get_current_weather",
                "description": "获取给定地点的天气",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "地理位置，比如北京市"
                        }
                    },
                    "required": ["location"]
                }
            }
        }
        """
        validated_tools = []

        for i, tool in enumerate(tools):
            try:
                # 验证必需字段
                if "Type" not in tool:
                    tool["Type"] = "function"
                elif tool["Type"] != "function":
                    logger.error(f"工具{i}: Type必须为'function'，当前值: {tool['Type']}")
                    continue

                if "function" not in tool:
                    logger.error(f"工具{i}: 缺少function字段")
                    continue

                function_def = tool["function"]

                # 验证function必需字段
                if "name" not in function_def:
                    logger.error(f"工具{i}: function缺少name字段")
                    continue

                if "description" not in function_def:
                    logger.warning(f"工具{i}: function建议包含description字段，当前工具: {function_def['name']}")

                # 验证name格式（应该是有效的函数名）
                name = function_def["name"]
                if not isinstance(name, str) or not name.replace('_', '').isalnum():
                    logger.error(f"工具{i}: function.name格式无效: {name}")
                    continue

                # 验证parameters格式（JSON Schema格式）
                if "parameters" in function_def:
                    params = function_def["parameters"]
                    if not isinstance(params, dict):
                        logger.error(f"工具{i}: parameters必须是对象，当前类型: {type(params)}")
                        continue

                    # 验证parameters的JSON Schema格式
                    if "type" not in params:
                        logger.error(f"工具{i}: parameters缺少type字段")
                        continue

                    if params["type"] != "object":
                        logger.warning(f"工具{i}: parameters.type建议为'object'，当前值: {params['type']}")

                    # 验证properties字段（如果存在）
                    if "properties" in params:
                        properties = params["properties"]
                        if not isinstance(properties, dict):
                            logger.error(f"工具{i}: parameters.properties必须是对象")
                            continue

                        # 验证每个属性的定义
                        for prop_name, prop_def in properties.items():
                            if not isinstance(prop_def, dict) or "type" not in prop_def:
                                logger.warning(f"工具{i}: 属性'{prop_name}'定义不完整，建议包含type字段")

                    # 验证required字段（如果存在）
                    if "required" in params:
                        required = params["required"]
                        if not isinstance(required, list):
                            logger.error(f"工具{i}: parameters.required必须是数组")
                            continue

                        # 检查required中的字段是否在properties中定义
                        if "properties" in params:
                            properties = params["properties"]
                            for req_field in required:
                                if req_field not in properties:
                                    logger.warning(f"工具{i}: required字段'{req_field}'未在properties中定义")

                validated_tools.append(tool)
                logger.info(f"工具定义验证通过: {function_def['name']}")

            except Exception as e:
                logger.error(f"工具{i}验证时发生异常: {e}")
                continue

        logger.info(f"Function Calling工具验证完成: {len(validated_tools)}/{len(tools)}个工具通过验证")
        return validated_tools

    async def start_voice_chat(
        self,
        room_id: str,
        task_id: str,
        user_id: str,
        webhook_url: str,
        custom_data: Dict[str, Any],
        character_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, str]:
        """
        启动火山引擎语音对话

        Args:
            room_id: RTC房间ID
            task_id: 任务ID
            user_id: 用户ID
            webhook_url: 事件回调URL
            custom_data: 自定义数据，将随Webhook事件发送
            character_config: 角色配置

        Returns:
            包含token、roomId、taskId的字典

        Raises:
            VolcanoAPIError: API调用失败
        """
        logger.info(f"启动语音对话 - TaskId: {task_id}, RoomId: {room_id}")

        # 构建请求配置，传递custom_data用于会话上下文
        config_data = self._build_voice_chat_config(
            room_id=room_id,
            task_id=task_id,
            user_id=user_id,
            webhook_url=webhook_url,
            character_config=character_config,
            custom_data=custom_data  # 传递会话上下文
        )

        # 验证模型
        validated_config = VolcanoStartVoiceChatRequest(**config_data)

        # 执行API调用（带重试机制）
        response_data = await self._call_volcano_api_with_retry(
            action="StartVoiceChat",
            data=validated_config.model_dump(by_alias=True, exclude_none=True),
            operation_desc=f"启动语音对话 - TaskId: {task_id}"
        )

        logger.info(f"语音对话启动成功 - TaskId: {task_id}")
        return {
            "token": response_data.get("token", ""),  # 根据实际API响应调整
            "roomId": room_id,
            "taskId": task_id,
            "userId": user_id
        }

    async def stop_voice_chat(self, task_id: str) -> bool:
        """
        停止火山引擎语音对话

        Args:
            task_id: 任务ID

        Returns:
            是否成功停止
        """
        logger.info(f"停止语音对话 - TaskId: {task_id}")

        config_data = {
            "AppId": self.app_id,
            "TaskId": task_id
        }

        try:
            await self._call_volcano_api_with_retry(
                action="StopVoiceChat",
                data=config_data,
                operation_desc=f"停止语音对话 - TaskId: {task_id}"
            )
            logger.info(f"语音对话停止成功 - TaskId: {task_id}")
            return True
        except Exception as e:
            logger.error(f"停止语音对话失败 - TaskId: {task_id}, Error: {e}")
            return False

    async def ban_user_stream(self, room_id: str, user_id: str, ban_audio: bool, ban_video: bool, duration_seconds: int = 600) -> bool:
        """
        封禁用户的音视频流 - [新增方法]

        用于危机干预场景，临时禁用用户的音频输入以防止进一步伤害

        Args:
            room_id: 房间ID
            user_id: 用户ID
            ban_audio: 是否封禁音频流
            ban_video: 是否封禁视频流
            duration_seconds: 封禁持续时间（秒），默认10分钟

        Returns:
            是否成功封禁
        """
        logger.warning(f"正在封禁用户 {user_id} 在房间 {room_id} 中的流（音频: {ban_audio}, 视频: {ban_video}）...")

        request_data = {
            "AppId": self.app_id,
            "RoomId": room_id,
            "UserId": user_id,
            "Audio": ban_audio,
            "Video": ban_video,
            "ForbiddenInterval": duration_seconds
        }

        try:
            await self._call_volcano_api_with_retry(
                action="BanUserStream",
                data=request_data,
                operation_desc=f"封禁用户流 - User: {user_id}, Room: {room_id}",
                version="2023-11-01"
            )
            logger.info(f"成功封禁用户 {user_id} 的流，持续时间: {duration_seconds}秒")
            return True
        except Exception as e:
            logger.error(f"封禁用户 {user_id} 的流失败: {e}")
            return False

    async def update_voice_chat(self, room_id: str, task_id: str, command: str, message: Optional[str] = None, interrupt_mode: Optional[int] = None) -> Dict[str, Any]:
        """
        更新语音对话，发送控制命令 - 符合官方文档规范

        根据官方文档，UpdateVoiceChat支持以下命令：
        - interrupt: 打断智能体
        - function: 传回工具调用信息指令（Message必须为JSON转义字符串）
        - ExternalTextToSpeech: 传入文本信息供TTS音频播放
        - ExternalPromptsForLLM: 传入自定义文本与用户问题拼接
        - ExternalTextToLLM: 传入外部问题送入LLM
        - FinishSpeechRecognition: 触发新一轮对话

        Args:
            room_id: 房间ID
            task_id: 任务ID
            command: 控制命令
            message: 命令消息（根据command类型格式不同）
            interrupt_mode: 中断模式（仅特定命令需要）

        Returns:
            包含调用结果的字典
        """
        logger.info(f"更新语音对话 - TaskId: {task_id}, Command: {command}")

        # 验证必要参数
        if not room_id or not task_id:
            raise ValueError("RoomId和TaskId不能为空")

        # 验证command类型（根据官方文档完整列表）
        valid_commands = [
            "interrupt",                    # 打断智能体
            "function",                    # 传回工具调用信息指令
            "ExternalTextToSpeech",        # 传入文本信息供TTS音频播放
            "ExternalPromptsForLLM",       # 传入自定义文本与用户问题拼接
            "ExternalTextToLLM",           # 传入外部问题送入LLM
            "FinishSpeechRecognition"      # 触发新一轮对话
        ]
        if command not in valid_commands:
            raise ValueError(f"不支持的命令类型: {command}，支持的命令: {valid_commands}")

        # 根据命令类型验证参数
        if command == "function" and not message:
            raise ValueError("function命令需要提供message参数（JSON转义字符串）")

        if command in ["ExternalTextToSpeech", "ExternalPromptsForLLM", "ExternalTextToLLM"] and not message:
            raise ValueError(f"{command}命令需要提供message参数")

        # 验证message格式（特定命令要求）
        if command == "function":
            try:
                # function命令的message必须是有效的JSON字符串
                json.loads(message)
            except (json.JSONDecodeError, TypeError):
                raise ValueError("function命令的message参数必须是有效的JSON字符串")

        # 构建请求数据
        request_data = {
            "AppId": self.app_id,
            "RoomId": room_id,
            "TaskId": task_id,
            "Command": command,
        }

        # 根据官方文档，某些命令需要Message参数
        if command in ["function", "ExternalTextToSpeech", "ExternalPromptsForLLM", "ExternalTextToLLM"]:
            if not message:
                raise ValueError(f"命令 {command} 需要提供Message参数")

            # 对于function命令，验证Message格式是否为有效的JSON字符串
            if command == "function":
                try:
                    # 验证是否为有效的JSON字符串
                    parsed = json.loads(message)
                    # 验证是否包含必要字段
                    if "ToolCallID" not in parsed or "Content" not in parsed:
                        raise ValueError("function命令的Message必须包含ToolCallID和Content字段")
                except json.JSONDecodeError:
                    raise ValueError("function命令的Message必须是有效的JSON字符串")

            request_data["Message"] = message

        # 根据官方文档，某些命令需要InterruptMode参数
        if command in ["ExternalTextToSpeech", "ExternalTextToLLM"] and interrupt_mode is not None:
            if interrupt_mode not in [1, 2, 3]:
                raise ValueError("InterruptMode必须是1(高优先级)、2(中优先级)或3(低优先级)")
            request_data["InterruptMode"] = interrupt_mode

        # 统一使用最新版本API
        api_version = "2024-12-01"

        try:
            result = await self._call_volcano_api_with_retry(
                action="UpdateVoiceChat",
                data=request_data,
                operation_desc=f"更新语音对话 - TaskId: {task_id}, Command: {command}",
                version=api_version
            )
            logger.info(f"更新语音对话成功 - TaskId: {task_id}, Command: {command}")
            return {
                "success": True,
                "result": result,
                "command": command,
                "task_id": task_id
            }
        except Exception as e:
            logger.error(f"更新语音对话失败 - TaskId: {task_id}, Command: {command}, Error: {e}")
            return {
                "success": False,
                "error": str(e),
                "command": command,
                "task_id": task_id
            }

    def _build_voice_chat_config(
        self,
        room_id: str,
        task_id: str,
        user_id: str,
        webhook_url: str,
        character_config: Optional[Dict[str, Any]] = None,
        custom_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        构建StartVoiceChat API的完整请求体字典
        严格遵循官方文档定义的嵌套结构
        """
        # 角色或默认的语音配置
        voice_settings = character_config.get("voice", {}) if character_config else {}

        # ASR配置
        asr_settings = character_config.get("asr", {}) if character_config else {}
        asr_mode = asr_settings.get("mode", "smallmodel")  # 默认使用smallmodel

        # TTS配置
        tts_provider = voice_settings.get("provider", "volcano")  # 默认为volcano

        # Function Calling配置
        function_calling_config = None
        if character_config and character_config.get("function_calling", {}).get("enabled", False):
            function_calling_config = character_config.get("function_calling", {})

        # 验证和处理Tools配置
        validated_tools = None
        if function_calling_config and "tools" in character_config.get("function_calling", {}):
            raw_tools = character_config["function_calling"]["tools"]
            if isinstance(raw_tools, list) and raw_tools:
                validated_tools = self._validate_function_calling_tools(raw_tools)
                if not validated_tools:
                    logger.warning("所有工具定义验证失败，Function Calling将被禁用")
                    function_calling_config = None

        # 构建Function Calling配置 - 符合官方文档规范
        function_calling_config_obj = None
        if function_calling_config and validated_tools:
            function_calling_config_obj = FunctionCallingConfig(
                ServerMessageUrl=webhook_url,  # 使用传入的webhook_url
                ServerMessageSignature=settings.VOLCENGINE_WEBHOOK_SECRET or "dummy-secret"
            )
            logger.info(f"启用Function Calling - ServerMessageUrl: {webhook_url}")
        else:
            logger.info("Function Calling未启用或工具验证失败")

        # 构建ASR配置 - 支持smallmodel和bigmodel
        if asr_mode == "bigmodel":
            asr_provider_params_data = {
                "Mode": "bigmodel",
                "AppId": settings.VOLCANO_ASR_APP_ID,
                "ApiResourceId": asr_settings.get("api_resource_id", "volc.bigasr.sauc.duration"),
                "StreamMode": asr_settings.get("stream_mode", 0),
                "context": asr_settings.get("context"),
                "boosting_table_id": asr_settings.get("boosting_table_id"),
                "correct_table_id": asr_settings.get("correct_table_id")
            }
            if not settings.VOLCANO_USE_LICENSE:
                asr_provider_params_data["AccessToken"] = settings.VOLCANO_ASR_ACCESS_TOKEN
            asr_provider_params = ASRProviderParamsBigModel(**asr_provider_params_data)
        else:  # smallmodel
            asr_provider_params = ASRProviderParamsSmallModel(
                Mode="smallmodel",
                AppId=settings.VOLCANO_ASR_APP_ID,
                Cluster=asr_settings.get("cluster", settings.VOLCANO_ASR_CLUSTER)
            )

        # 构建精确的、符合文档的ASR配置
        asr_config = ASRConfig(
            Provider="volcano",  # 修正：官方文档指定为"volcano"
            ProviderParams=asr_provider_params,
            # 添加官方文档要求的VAD配置
            VADConfig={
                "SilenceTime": asr_settings.get("silence_time", 600)  # 默认600ms
            },
            # 添加音量增益配置
            VolumeGain=asr_settings.get("volume_gain", 1.0),
            # 添加打断配置
            InterruptConfig={
                "InterruptSpeechDuration": asr_settings.get("interrupt_speech_duration", 0),
                "InterruptKeywords": asr_settings.get("interrupt_keywords", [])
            },
            # 添加对话触发模式
            TurnDetectionMode=asr_settings.get("turn_detection_mode", 0)
        )

        # 构建TTS Provider Params
        tts_app_params = {
            "appid": settings.VOLCANO_TTS_APP_ID
        }
        # 仅在需要时（如volcano_bidirection）添加token
        if tts_provider == "volcano_bidirection" and not settings.VOLCANO_USE_LICENSE:
            tts_app_params["token"] = settings.VOLCANO_TTS_ACCESS_TOKEN
        else:
            # 常规volcano provider需要cluster
            tts_app_params["cluster"] = voice_settings.get("cluster", settings.VOLCANO_TTS_CLUSTER)

        tts_provider_params_data = {
            "app": TTSProviderParamsApp(**tts_app_params),
            "audio": TTSProviderParamsAudio(
                voice_type=voice_settings.get("voice_type", "BV001_streaming"),
                speed_ratio=voice_settings.get("speed_ratio"),
                volume_ratio=voice_settings.get("volume_ratio"),
                pitch_ratio=voice_settings.get("pitch_ratio"),
                speech_rate=voice_settings.get("speech_rate"),
                pitch_rate=voice_settings.get("pitch_rate")
            )
        }
        if tts_provider == "volcano_bidirection":
            tts_provider_params_data["ResourceId"] = voice_settings.get("resource_id", "volc.service_type.10029")

        tts_provider_params = TTSProviderParams(**tts_provider_params_data)

        # 构建精确的、符合文档的TTS配置
        tts_config = TTSConfig(
            Provider=tts_provider,
            ProviderParams=tts_provider_params,
            # 添加官方文档要求的IgnoreBracketText字段
            IgnoreBracketText=voice_settings.get("ignore_bracket_text", [])  # 过滤指定标点符号中的文字
        )

        # 构建精确的、符合文档的配置 - 添加必需字段
        config = VoiceChatInteractionConfig(
            Type="VolcASRTTSLLMBot",  # 添加必需的Type字段
            ASRConfig=asr_config,
            TTSConfig=tts_config,
            LLMConfig=LLMConfig(
                **self._build_llm_config_data(character_config),
                Tools=validated_tools  # 使用验证后的工具定义
            ),
            FunctionCallingConfig=function_calling_config_obj,
            InterruptMode=0
        )

        agent_config = AgentConfig(
            TargetUserId=[user_id],
            UserId=f"Bot_{task_id}",
            WelcomeMessage=character_config.get("personality", {}).get("greeting", "你好，有什么可以帮助你的吗？") if character_config else "你好，有什么可以帮助你的吗？",
            EnableConversationStateCallback=True,
            ServerMessageURLForRTS=webhook_url,
            ServerMessageSignatureForRTS=settings.VOLCENGINE_WEBHOOK_SECRET or "dummy-secret"
        )

        full_request = VolcanoStartVoiceChatRequest(
            AppId=self.app_id,
            RoomId=room_id,
            TaskId=task_id,
            Config=config,
            AgentConfig=agent_config
        )

        # 构建完整请求体，添加Custom字段以传递会话上下文
        request_dict = full_request.model_dump(by_alias=True, exclude_none=True)

        # 添加Custom字段 - 关键修复：确保Webhook回调时能获取会话上下文
        if custom_data:
            # 使用传入的custom_data，确保会话上下文正确传递
            request_dict["Custom"] = json.dumps(custom_data, ensure_ascii=False)
        else:
            # 默认的会话上下文
            default_custom = {
                "sessionId": f"session_{task_id}",
                "userId": user_id,
                "characterId": character_config.get("character_id", "default") if character_config else "default",
                "roomId": room_id,
                "taskId": task_id
            }
            request_dict["Custom"] = json.dumps(default_custom, ensure_ascii=False)

        return request_dict

    async def _call_volcano_api_with_retry(
        self,
        action: str,
        data: Dict[str, Any],
        operation_desc: str,
        version: str = "2024-12-01"
    ) -> Dict[str, Any]:
        """
        调用火山引擎API（带重试机制）

        实现3次重试 + 指数退避策略
        """
        last_exception = None

        for attempt in range(self.max_retries + 1):  # 原始调用 + 3次重试
            try:
                if attempt > 0:
                    # 指数退避：2^attempt秒
                    delay = 2 ** attempt
                    logger.info(f"{operation_desc} - 第{attempt}次重试，延迟{delay}秒")
                    await asyncio.sleep(delay)

                return await self._call_volcano_api(action, data, version)

            except asyncio.TimeoutError as e:
                last_exception = e
                logger.warning(f"{operation_desc} - 第{attempt + 1}次尝试超时")
                if attempt == self.max_retries:
                    break
                continue

            except httpx.HTTPStatusError as e:
                last_exception = e
                if e.response.status_code == 401:
                    # 认证失败，不重试
                    logger.error(f"{operation_desc} - 认证失败: {e}")
                    raise VolcanoAPIError(
                        error_code="VOLCANO_AUTH_ERROR",
                        error_message="火山引擎认证失败",
                        details={"status_code": e.response.status_code}
                    )
                elif e.response.status_code == 429:
                    # 限流，需要重试
                    rate_limit_reset = e.response.headers.get("Rate-Limit-Reset", "2")
                    delay = max(int(rate_limit_reset), 2 ** attempt)
                    logger.warning(f"{operation_desc} - 限流，延迟{delay}秒后重试")
                    if attempt < self.max_retries:
                        await asyncio.sleep(delay)
                        continue
                    else:
                        break
                else:
                    # 其他HTTP错误
                    logger.error(f"{operation_desc} - HTTP错误: {e}")
                    raise VolcanoAPIError(
                        error_code="VOLCANO_API_ERROR",
                        error_message=f"火山引擎API错误: {e.response.status_code}",
                        details={
                            "status_code": e.response.status_code,
                            "response": e.response.text
                        }
                    )

            except Exception as e:
                last_exception = e
                logger.error(f"{operation_desc} - 未知错误: {e}")
                if attempt == self.max_retries:
                    break
                continue

        # 所有重试失败
        error_message = f"火山引擎服务暂时不可用，请稍后重试 (尝试了{self.max_retries + 1}次)"
        logger.error(f"{operation_desc} - 最终失败: {error_message}")
        raise VolcanoAPIError(
            error_code="VOLCANO_SERVICE_UNAVAILABLE",
            error_message=error_message,
            details={"last_exception": str(last_exception)}
        )

    async def _call_volcano_api(self, action: str, data: Dict[str, Any], version: str = "2024-12-01") -> Dict[str, Any]:
        """
        实际调用火山引擎API
        """
        url = f"{self.base_url}?Action={action}&Version={version}"

        headers = {
            "Content-Type": "application/json",
            "Authorization": self._generate_auth_header(action, data, version)
        }

        async with httpx.AsyncClient(timeout=self.timeout) as client:
            logger.debug(f"调用火山引擎API: {action}")
            response = await client.post(url, json=data, headers=headers)
            response.raise_for_status()

            result = response.json()
            logger.debug(f"火山引擎API响应: {result}")

            # 检查业务错误
            if result.get("Result") != "ok":
                raise VolcanoAPIError(
                    error_code="VOLCANO_BUSINESS_ERROR",
                    error_message=f"火山引擎业务错误: {result}",
                    details=result
                )

            return result

    def get_signed_headers(self, service: str, host: str, region: str, method: str, path: str, query_params: dict, body: bytes) -> dict:
        """
        生成火山V4签名的Headers。

        Args:
            service: 服务名称 (如 "rtc")
            host: 主机名 (如 "rtc.volcengineapi.com")
            region: 区域 (如 "cn-beijing")
            method: HTTP方法 (如 "POST")
            path: 请求路径 (如 "/")
            query_params: 查询参数字典
            body: 请求体字节数据

        Returns:
            包含签名的请求头字典
        """
        if not self.access_key:
            raise ValueError("Missing access_key configuration")
        if not self.secret_key:
            raise ValueError("Missing secret_key configuration")

        # 使用UTC时间
        t = datetime.datetime.now(datetime.timezone.utc)
        x_date = t.strftime('%Y%m%dT%H%M%SZ')  # ISO8601格式
        date_stamp = t.strftime('%Y%m%d')

        # 1. 创建规范请求 (CanonicalRequest)
        # 规范化URI - 火山引擎大部分API的URI均为"/"
        canonical_uri = quote(path, safe='/~') if path != "/" else "/"

        # 规范化查询字符串 - 按参数名ASCII升序排序
        canonical_querystring = urlencode(sorted(query_params.items()))

        # 规范化头部 - 必须包含host和x-date
        canonical_headers = f'host:{host}\n'
        canonical_headers += f'x-date:{x_date}\n'
        signed_headers = 'host;x-date'

        # 计算请求体的SHA256哈希值
        payload_hash = hashlib.sha256(body).hexdigest()

        # 组装规范请求
        canonical_request = '\n'.join([
            method,
            canonical_uri,
            canonical_querystring,
            canonical_headers,
            signed_headers,
            payload_hash
        ])

        # 2. 创建待签名字符串 (StringToSign)
        algorithm = 'HMAC-SHA256'
        credential_scope = f'{date_stamp}/{region}/{service}/request'
        string_to_sign = '\n'.join([
            algorithm,
            x_date,
            credential_scope,
            hashlib.sha256(canonical_request.encode('utf-8')).hexdigest()
        ])

        # 3. 计算签名
        signing_key = self._get_signature_key(self.secret_key.encode('utf-8'), date_stamp, region, service)
        signature = hmac.new(signing_key, string_to_sign.encode('utf-8'), hashlib.sha256).hexdigest()

        # 4. 组合最终的Headers
        authorization_header = f"{algorithm} Credential={self.access_key}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}"

        headers = {
            'X-Date': x_date,  # 火山引擎使用X-Date而不是x-amz-date
            'Authorization': authorization_header,
            'Content-Type': 'application/json; charset=utf-8',
            'Host': host,
            'X-Content-Sha256': payload_hash  # 火山引擎要求的请求体哈希
        }
        return headers

    def _get_signature_key(self, key, date_stamp, region, service):
        """生成签名密钥"""
        k_date = hmac.new(key, date_stamp.encode('utf-8'), hashlib.sha256).digest()
        k_region = hmac.new(k_date, region.encode('utf-8'), hashlib.sha256).digest()
        k_service = hmac.new(k_region, service.encode('utf-8'), hashlib.sha256).digest()
        k_signing = hmac.new(k_service, b'request', hashlib.sha256).digest()
        return k_signing

    def _generate_auth_header(self, action: str, data: Dict[str, Any], version: str = "2024-12-01") -> str:
        """
        生成火山引擎API认证头
        注意：这个方法已被get_signed_headers取代，保留用于向后兼容
        """
        # 使用V4签名算法生成认证头
        body = json.dumps(data).encode('utf-8')
        headers = self.get_signed_headers(
            service="rtc",
            host=self.base_url.replace("https://", "").replace("http://", ""),
            region="cn-beijing",
            method="POST",
            path="/",
            query_params={"Action": action, "Version": version},
            body=body
        )
        return headers.get("Authorization", f"Bearer {self.access_key}")

# 依赖注入
_volcano_client_service = None

def get_volcano_client_service() -> VolcanoClientService:
    """获取火山客户端服务实例"""
    global _volcano_client_service
    if _volcano_client_service is None:
        _volcano_client_service = VolcanoClientService()
    return _volcano_client_service
