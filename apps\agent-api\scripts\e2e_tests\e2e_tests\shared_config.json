{"auth": {"access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIzYTkxYWYxNS1kNjNmLTQ2NWItOWU4YS1lNTViZDIwMDM3MmYiLCJ1c2VyX2lkIjoiM2E5MWFmMTUtZDYzZi00NjViLTllOGEtZTU1YmQyMDAzNzJmIiwidHlwZSI6ImFjY2VzcyIsImV4cCI6MTc1MzExMzQ1MCwiaWF0IjoxNzUzMTExNjUwfQ.pzhAqt4loM0zPaZM9poiA1tgsb9HKfiA-GaEZ_9MgGE", "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIzYTkxYWYxNS1kNjNmLTQ2NWItOWU4YS1lNTViZDIwMDM3MmYiLCJ1c2VyX2lkIjoiM2E5MWFmMTUtZDYzZi00NjViLTllOGEtZTU1YmQyMDAzNzJmIiwidHlwZSI6InJlZnJlc2giLCJleHAiOjE3NTM3MTY0NTAsImlhdCI6MTc1MzExMTY1MH0.jHpo50OTSvYxbLENjp7jezYEOgIUX1o0_zlg9zBcrP0", "user_id": "3a91af15-d63f-465b-9e8a-e55bd200372f", "updated_at": "2025-07-21T23:27:30.665191"}, "device_id": "test-device-d0f6aeb7-c668-4ad1-8441-6da4798dbc8e", "platform": "android", "app_version": "1.0.0", "nickname": "测试用户", "character_id": "df7616a7-5db0-4119-88fc-9ca2eb5e7774", "test_results": {"onboarding": {"onboarding_completed": true, "character_id": "df7616a7-5db0-4119-88fc-9ca2eb5e7774", "nickname": "测试用户", "core_needs": ["情感陪伴", "健康咨询"], "interests": ["音乐", "阅读"], "communication_style_preference": "温和", "character_info": {"name": "测试角色", "role": "朋友", "voice_id": "voice_001"}, "completed_at": "2025-07-21T23:27:40.370689", "test_name": "02_auth"}, "user_profile": {"profile_updated": true, "current_nickname": "测试用户_updated", "age_range": "55-65", "core_needs": ["情感陪伴", "健康咨询"], "interests": ["音乐", "阅读"], "communication_style_preference": "温和", "completed_at": "2025-07-21T23:27:46.131620", "test_name": "03_user"}, "character_binding": {"character_bound": true, "character_id": "df7616a7-5db0-4119-88fc-9ca2eb5e7774", "binding_response": {"success": true, "message": "Character bound successfully"}, "completed_at": "2025-07-21T23:27:51.367599", "test_name": "03_user"}, "session_creation": {"session_created": true, "session_id": "516d34c2-0e52-4eac-b182-92150faa5816", "character_id": "df7616a7-5db0-4119-88fc-9ca2eb5e7774", "topic": "测试会话", "completed_at": "2025-07-21T23:28:12.046254", "test_name": "05_session"}, "reminder_creation": {"reminder_created": true, "reminder_id": "e2e08073-80ca-483e-bfeb-96818e100450", "content": "测试提醒内容", "reminder_time": "2025-07-22T00:29:44.074687", "description": "这是一个测试提醒的详细描述", "status": "pending", "completed_at": "2025-07-21T23:29:44.778479", "test_name": "07_reminder"}}}