"""
RTC Webhook事件模型 - 基于火山引擎官方文档
"""
from pydantic import BaseModel, Field, field_validator
from typing import Optional, Dict, Any, List, Union
from datetime import datetime, timezone
import json
import time


class AsrPayload(BaseModel):
    """ASR事件载荷"""
    text: str = Field(..., description="识别的文本内容")
    timestamp: Optional[str] = Field(None, description="时间戳")
    confidence: Optional[float] = Field(None, description="置信度")
    is_final: Optional[bool] = Field(None, description="是否为最终结果")
    sequence: Optional[int] = Field(None, description="序列号")


class FunctionCallPayload(BaseModel):
    """Function Calling事件载荷 - 根据火山引擎官方文档"""
    message: List[Dict[str, Any]] = Field(..., description="工具调用指令消息详情数组")
    signature: Optional[str] = Field(None, description="签名，用于鉴权")

    def get_tool_calls(self) -> List[Dict[str, Any]]:
        """解析工具调用列表"""
        return self.message or []


class VoiceChatPayload(BaseModel):
    """VoiceChat智能体事件载荷 - 符合官方文档规范"""
    AppId: str = Field(..., description="音视频应用的唯一标识")
    BusinessId: Optional[str] = Field(None, description="业务标识")
    RoomId: str = Field(..., description="房间ID，房间的唯一标识")
    TaskId: str = Field(..., description="智能体任务ID")
    UserID: str = Field(..., description="说话人UserId")
    RoundID: int = Field(..., description="对话轮次，从0开始计数")
    EventTime: int = Field(..., description="该事件在RTC服务器上发生的Unix时间戳(ms)")
    EventType: int = Field(..., description="任务状态类型: 0=状态变化, 1=任务错误")
    RunStage: str = Field(..., description="状态详情")
    ErrorInfo: Optional[Dict[str, Any]] = Field(None, description="任务错误详细信息")

    class Config:
        # RunStage可能的值
        schema_extra = {
            "example": {
                "AppId": "661e****543cf",
                "RoomId": "room1",
                "TaskId": "task1",
                "UserID": "user1",
                "RoundID": 0,
                "EventTime": 1611736812853,
                "EventType": 0,
                "RunStage": "asrFinish"
            }
        }


# 替换原来的ConversationStatusPayload
class ConversationStatusPayload(BaseModel):
    """对话状态变化事件载荷 - 保持向后兼容"""
    status: str = Field(..., description="状态：thinking, speaking, listening, idle")
    agent_id: Optional[str] = Field(None, description="智能体ID")
    timestamp: Optional[str] = Field(None, description="状态变化时间戳")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外元数据")


class RtcWebhookRequest(BaseModel):
    """火山RTC Webhook请求模型 - 符合官方字段名规范"""
    # 官方字段名 (大写)
    EventType: str = Field(..., description="事件类型")
    EventData: str = Field(..., description="具体的事件内容，格式为Json")
    EventTime: str = Field(..., description="事件产生时间，ISO-8601格式")
    EventId: str = Field(..., description="事件Id，具有唯一性")
    AppId: str = Field(..., description="RTC应用的唯一标识")
    Version: str = Field(..., description="事件的版本号")
    Nonce: str = Field(..., description="签名随机数4位")
    # Signature在验证时从header获取，不参与模型序列化

    # 内部字段，在parse_payload后填充
    payload: Union[AsrPayload, FunctionCallPayload, VoiceChatPayload, ConversationStatusPayload, Dict[str, Any]] = Field(None, exclude=True)
    custom: Optional[str] = Field(None, exclude=True)
    request_id: Optional[str] = Field(None, exclude=True)

    # 向后兼容的旧字段名 (小写) - 临时保留
    event_type: Optional[str] = Field(None, description="旧版字段名，向后兼容")

    @field_validator('EventData')
    @classmethod
    def validate_event_data(cls, value: str) -> str:
        """验证EventData字段是否为有效的JSON字符串"""
        try:
            json.loads(value)
        except json.JSONDecodeError:
            raise ValueError("EventData字段必须是有效的JSON字符串")
        return value

    @classmethod
    def __pydantic_init_subclass__(cls, **kwargs):
        """添加向后兼容性支持"""
        super().__pydantic_init_subclass__(**kwargs)

    def __init__(self, **data):
        """支持旧字段名的构造函数，实现向后兼容性"""
        # 如果传入的是旧字段名格式，进行映射
        if 'event_type' in data and 'EventType' not in data:
            data['EventType'] = data['event_type']
        if 'event_data' in data and 'EventData' not in data:
            data['EventData'] = data.get('event_data', '{}')
        if 'event_time' in data and 'EventTime' not in data:
            data['EventTime'] = data.get('event_time', '')
        if 'event_id' in data and 'EventId' not in data:
            data['EventId'] = data.get('event_id', '')
        if 'app_id' in data and 'AppId' not in data:
            data['AppId'] = data.get('app_id', '')
        if 'version' in data and 'Version' not in data:
            data['Version'] = data.get('version', '')
        if 'nonce' in data and 'Nonce' not in data:
            data['Nonce'] = data.get('nonce', '')

        # 如果是最小的旧格式数据，填充必需字段
        if 'payload' in data and 'EventData' not in data:
            data['EventData'] = json.dumps({'Payload': data['payload']})
        if 'timestamp' in data and 'EventTime' not in data:
            import datetime
            timestamp = data['timestamp']
            if isinstance(timestamp, (int, float)):
                data['EventTime'] = datetime.datetime.fromtimestamp(timestamp, tz=datetime.timezone.utc).isoformat().replace('+00:00', 'Z')
            else:
                data['EventTime'] = str(timestamp)

        # 为缺失的必需字段提供默认值
        if 'EventId' not in data:
            data['EventId'] = f"compat_{int(time.time())}"
        if 'AppId' not in data:
            data['AppId'] = "backward_compat"
        if 'Version' not in data:
            data['Version'] = "2024-06-01"
        if 'Nonce' not in data:
            data['Nonce'] = "0000"

        super().__init__(**data)

    def parse_payload(self):
        """解析EventData字段填充内部payload"""
        try:
            event_data_dict = json.loads(self.EventData)
        except json.JSONDecodeError:
            raise ValueError("无法解析EventData为JSON格式")

        self.request_id = event_data_dict.get('RequestId')
        self.custom = event_data_dict.get('Custom')

        # VoiceChat事件的EventData直接包含所有字段，不是嵌套在Payload中
        if self.EventType == "VoiceChat":
            self.payload = VoiceChatPayload(**event_data_dict)
        else:
            # 其他事件类型使用Payload嵌套结构
            payload_data = event_data_dict.get('Payload', {})

            if self.EventType == "asr_result":
                self.payload = AsrPayload(**payload_data)
            elif self.EventType == "function_call":
                self.payload = FunctionCallPayload(**payload_data)
            else:
                # 未知事件类型，保存原始数据
                self.payload = payload_data

    def get_custom_data(self) -> Dict[str, Any]:
        """解析custom字段并返回字典"""
        if self.custom:
            try:
                return json.loads(self.custom)
            except json.JSONDecodeError:
                return {}
        return {}

    def get_session_id(self) -> Optional[str]:
        """从custom字段中提取sessionId"""
        custom_data = self.get_custom_data()
        return custom_data.get('sessionId')

    def get_user_id(self) -> Optional[str]:
        """从custom字段中提取userId"""
        custom_data = self.get_custom_data()
        return custom_data.get('userId')

    def get_character_id(self) -> Optional[str]:
        """从custom字段中提取characterId"""
        custom_data = self.get_custom_data()
        return custom_data.get('characterId')


class RtcWebhookResponse(BaseModel):
    """火山RTC Webhook响应模型"""
    text: str = Field(..., description="AI生成的回复文本")
    status: str = Field("success", description="处理状态")
    request_id: Optional[str] = Field(None, description="请求ID")
    timestamp: Optional[str] = Field(None, description="响应时间戳")

    def model_post_init(self, __context: Any) -> None:
        """模型初始化后设置时间戳"""
        if self.timestamp is None:
            self.timestamp = datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z')


class VolcengineErrorInfo(BaseModel):
    """火山引擎标准错误信息结构"""
    CodeN: Optional[int] = Field(None, description="网关的错误码")
    Code: str = Field(..., description="API的错误码")
    Message: str = Field(..., description="具体的错误信息")


class VolcengineResponseMetadata(BaseModel):
    """火山引擎标准响应元数据结构"""
    RequestId: str = Field(..., description="请求标识")
    Action: str = Field(..., description="接口名称")
    Version: str = Field(..., description="接口版本")
    Service: str = Field("rtc", description="接口所属服务")
    Region: str = Field("cn-north-1", description="地域参数")
    Error: Optional[VolcengineErrorInfo] = Field(None, description="仅在请求失败时返回")


class VolcengineStandardErrorResponse(BaseModel):
    """火山引擎标准错误响应模型 - 符合官方文档规范"""
    ResponseMetadata: VolcengineResponseMetadata
    Result: Optional[Dict[str, Any]] = Field(default_factory=dict, description="请求结果")


class RtcWebhookErrorResponse(BaseModel):
    """火山RTC Webhook错误响应模型 - 兼容性保留"""
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    status: str = Field("error", description="处理状态")
    request_id: Optional[str] = Field(None, description="请求ID")
    timestamp: Optional[str] = Field(None, description="响应时间戳")

    def model_post_init(self, __context: Any) -> None:
        """模型初始化后设置时间戳"""
        if self.timestamp is None:
            self.timestamp = datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z')


class PrepareSessionRequest(BaseModel):
    """会话准备请求模型 - 用户ID从JWT Token获取"""
    # 移除userId字段，现在从JWT Token中获取
    sessionId: str = Field(..., description="会话ID")
    characterId: str = Field(..., description="AI角色ID")

    class Config:
        json_schema_extra = {
            "example": {
                "sessionId": "session_67890",
                "characterId": "compassionate_listener"
            }
        }


class InternalPrepareSessionRequest(BaseModel):
    """内部使用的完整会话准备请求模型 - 包含从JWT Token获取的userId"""
    userId: str = Field(..., description="用户ID - 从JWT Token获取")
    sessionId: str = Field(..., description="会话ID")
    characterId: str = Field(..., description="AI角色ID")

    class Config:
        json_schema_extra = {
            "example": {
                "userId": "user_12345",
                "sessionId": "session_67890",
                "characterId": "compassionate_listener"
            }
        }


class PrepareSessionResponse(BaseModel):
    """会话准备响应模型"""
    token: str = Field(..., description="RTC连接令牌")
    roomId: str = Field(..., description="RTC房间ID")
    userId: str = Field(..., description="用户ID")
    taskId: str = Field(..., description="火山引擎任务ID")

    class Config:
        json_schema_extra = {
            "example": {
                "token": "rtc_token_123",
                "roomId": "room_456",
                "userId": "user_12345",
                "taskId": "task_789"
            }
        }


class EndSessionRequest(BaseModel):
    """结束会话请求模型 - 用户ID从JWT Token获取"""
    # 移除userId字段，现在从JWT Token中获取
    sessionId: str = Field(..., description="会话ID")
    taskId: str = Field(..., description="火山引擎任务ID")


class EndSessionResponse(BaseModel):
    """结束会话响应模型"""
    success: bool = Field(..., description="是否成功结束")
    message: Optional[str] = Field(None, description="响应消息")


class SessionStatusResponse(BaseModel):
    """会话状态响应模型"""
    sessionId: str = Field(..., description="会话ID")
    status: str = Field(..., description="会话状态: preparing|active|ended|error")
    startTime: Optional[datetime] = Field(None, description="开始时间")
    endTime: Optional[datetime] = Field(None, description="结束时间")
    taskId: Optional[str] = Field(None, description="火山引擎任务ID")


class SessionConfigResponse(BaseModel):
    """会话配置响应模型"""
    llmConfig: Dict[str, Any] = Field(..., description="LLM配置")
    voiceConfig: Dict[str, Any] = Field(..., description="语音配置")
    characterConfig: Dict[str, Any] = Field(..., description="角色配置")


class UpdateSessionConfigRequest(BaseModel):
    """更新会话配置请求模型"""
    voiceConfig: Optional[Dict[str, Any]] = Field(None, description="语音配置")
    llmConfig: Optional[Dict[str, Any]] = Field(None, description="LLM配置")


class UpdateSessionCommandRequest(BaseModel):
    """
    更新会话命令请求模型
    用于发送如interrupt, function, ExternalTextToSpeech等命令
    """
    command: str = Field(..., description="要执行的命令 (例如 'interrupt')")
    message: Optional[str] = Field(None, description="命令附带的消息体，JSON字符串格式")
    interrupt_mode: Optional[int] = Field(None, description="中断模式，用于ExternalTextToSpeech等命令")


# --- 新增和重构火山引擎 StartVoiceChat 的精确模型 ---

# ASRConfig Models
class ASRProviderParamsBigModel(BaseModel):
    Mode: str = "bigmodel"
    AppId: str
    AccessToken: str
    ApiResourceId: Optional[str] = "volc.bigasr.sauc.duration"
    StreamMode: Optional[int] = 0
    context: Optional[str] = None
    boosting_table_id: Optional[str] = None
    correct_table_id: Optional[str] = None

class ASRProviderParamsSmallModel(BaseModel):
    Mode: str = "smallmodel"
    AppId: str
    Cluster: str

class VADConfig(BaseModel):
    SilenceTime: Optional[int] = 600

class InterruptConfig(BaseModel):
    InterruptSpeechDuration: Optional[int] = 0
    InterruptKeywords: Optional[List[str]] = None

class ASRConfig(BaseModel):
    Provider: str = "volcano"
    ProviderParams: Union[ASRProviderParamsBigModel, ASRProviderParamsSmallModel]
    VADConfig: Optional[VADConfig] = None
    InterruptConfig: Optional[InterruptConfig] = None
    TurnDetectionMode: Optional[int] = 0

# TTSConfig Models
class TTSProviderParamsApp(BaseModel):
    appid: str
    token: Optional[str] = None
    cluster: Optional[str] = None

class TTSProviderParamsAudio(BaseModel):
    voice_type: str
    speed_ratio: Optional[float] = None
    volume_ratio: Optional[float] = None
    pitch_ratio: Optional[float] = None
    pitch_rate: Optional[int] = None
    speech_rate: Optional[int] = None

class TTSProviderParamsAdditions(BaseModel):
    enable_latex_tn: Optional[bool] = None
    disable_markdown_filter: Optional[bool] = None
    enable_language_detector: Optional[bool] = None

class TTSProviderParams(BaseModel):
    app: TTSProviderParamsApp
    audio: TTSProviderParamsAudio
    Additions: Optional[TTSProviderParamsAdditions] = None
    ResourceId: Optional[str] = None


class TTSConfig(BaseModel):
    Provider: str
    ProviderParams: TTSProviderParams
    IgnoreBracketText: Optional[List[int]] = None

# LLMConfig Models
class LLMFunction(BaseModel):
    name: str
    description: Optional[str] = None
    parameters: Dict[str, Any]

class LLMTool(BaseModel):
    Type: str = "function"
    function: LLMFunction

class LLMConfig(BaseModel):
    Mode: str
    EndPointId: Optional[str] = None
    BotId: Optional[str] = None
    Temperature: Optional[float] = 0.1
    MaxTokens: Optional[int] = 1024
    TopP: Optional[float] = 0.3
    SystemMessages: Optional[List[str]] = None
    UserPrompts: Optional[List[Dict[str, str]]] = None
    HistoryLength: Optional[int] = 3
    Tools: Optional[List[LLMTool]] = None
    Prefill: Optional[bool] = False
    # VisionConfig, StorageConfig etc. can be added here if needed

# FunctionCallingConfig Model
class FunctionCallingConfig(BaseModel):
    ServerMessageUrl: str
    ServerMessageSignature: str

# Main Config Model for StartVoiceChat
class VoiceChatInteractionConfig(BaseModel):
    ASRConfig: ASRConfig
    TTSConfig: TTSConfig
    LLMConfig: LLMConfig
    FunctionCallingConfig: Optional[FunctionCallingConfig] = None
    InterruptMode: Optional[int] = 0

# AgentConfig Model
class AgentConfig(BaseModel):
    TargetUserId: List[str]
    WelcomeMessage: Optional[str] = None
    UserId: str
    EnableConversationStateCallback: Optional[bool] = True
    ServerMessageURLForRTS: Optional[str] = None
    ServerMessageSignatureForRTS: Optional[str] = None

# StartVoiceChat请求模型
class VolcanoStartVoiceChatRequest(BaseModel):
    """
    火山引擎StartVoiceChat的完整、精确请求模型
    基于官方文档 2024-12-01 版本，已应用参数格式验证
    """
    AppId: str = Field(..., description="音视频应用的唯一标识")
    RoomId: str = Field(..., description="房间ID，需与客户端SDK使用的RoomId保持一致")
    TaskId: str = Field(..., description="智能体任务ID，全局唯一")
    Configuration: VoiceChatInteractionConfig = Field(..., description="智能体交互服务配置", alias="Config")
    AgentConfiguration: AgentConfig = Field(..., description="智能体配置", alias="AgentConfig")

    model_config = {
        "json_schema_extra": {
            "example": {
                "AppId": "661e543cf",
                "RoomId": "room_12345",
                "TaskId": "task_67890",
                "Config": {
                    "ASRConfig": {
                        "Provider": "volcano",
                        "ProviderParams": {
                            "Mode": "bigmodel",
                            "AppId": "93****21",
                            "AccessToken": "MOaOaa_VQ6****1B34UHA4h5B"
                        }
                    },
                    "TTSConfig": {
                        "Provider": "volcano",
                        "ProviderParams": {
                            "app": {
                                "appid": "94****11",
                                "cluster": "volcano_tts"
                            },
                            "audio": {
                                "voice_type": "BV001_streaming"
                            }
                        }
                    },
                    "LLMConfig": {
                        "Mode": "ArkV3",
                        "EndPointId": "ep-22****212"
                    }
                },
                "AgentConfig": {
                    "TargetUserId": ["user_12345"],
                    "UserId": "agent_001"
                }
            }
        }
    }


class VolcanoAPIError(Exception):
    """火山引擎API错误异常"""

    def __init__(self, error_code: str, error_message: str, details: Optional[Dict[str, Any]] = None):
        self.error_code = error_code
        self.error_message = error_message
        self.details = details or {}
        super().__init__(f"{error_code}: {error_message}")
