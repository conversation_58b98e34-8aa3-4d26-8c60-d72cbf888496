# .env - 心桥项目完整密钥配置

# =============================================================
#  第一部分: 应用基础配置
# =============================================================
APP_ENV=development
ENVIRONMENT=test
DEBUG=true
LOG_LEVEL=DEBUG
API_V1_PREFIX=/api/v1
API_BASE_URL=https://callback.xinqiao.xin
API_PORT_HOST=8000

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:19006

# =============================================================
#  第二部分: 安全配置
# =============================================================
# 应用内部加密、签名等安全操作
SECRET_KEY=81f3a2b0093a0422d244762823540c9223261458deee1a640a9f069c7177fb99
JWT_SECRET_KEY=81f3a2b0093a0422d244762823540c9223261458deee1a640a9f069c7177fb99

# JWT Token配置
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# =============================================================
#  第三部分: Supabase (数据库 & 用户认证)
# =============================================================
# 用途: 数据库连接, 用户认证, 后端服务级操作
# 获取路径: Supabase 项目后台 -> Project Settings -> API & Database

# 项目URL
SUPABASE_URL=https://cqclagklqvtdgvzoxrhz.supabase.co

# 用于前端客户端的公共匿名密钥
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNxY2xhZ2tscXZ0ZGd2em94cmh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE0MzQ4OTcsImV4cCI6MjA2NzAxMDg5N30.1VPwdchQ7fTlgEE-Jyp7FN_eL6CDTcCZehKN8O01_E0

# **[后端专用]** 拥有最高权限的服务角色密钥，切勿泄露到前端
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNxY2xhZ2tscXZ0ZGd2em94cmh6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTQzNDg5NywiZXhwIjoyMDY3MDEwODk3fQ.Rg6pfuaPvb15r7fPE9XKlnWjKSps-SqOZoCAVoGieeA

# **[后端专用]** 用于验证JWT Token的签名密钥
SUPABASE_JWT_SECRET=odIlGI0b2vTwXFuDWMW/DQFft+edNlsl5sMctQMsDDnIeF10BswhA24mNlkpy5THgCXouwO3MKhVwahIppOqMg==

# **[后端专用]** 数据库的直接连接字符串
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# =============================================================
#  第四部分: 火山引擎 (Volcano Engine)
# =============================================================
# 用途: RTC实时通信, 语音识别(ASR), 语音合成(TTS), 豆包大模型(LLM)
# 获取路径: 火山引擎控制台

# --- 核心账户凭证 ---
# 强烈建议使用子账号的AK/SK，并仅授予所需服务的权限
VOLCANO_ACCESS_KEY_ID=AKLTMDdjZTVhODgyY2YyNDc3ZTk4MDBiZTI4ODBmMDRjYmE
VOLCANO_SECRET_ACCESS_KEY=TldNMU4yUmxNelEzTVRaak5ETTNObUZoTVdNNE1HVmlNalpsTXpnNE5ETQ==

# --- 修复：添加缺失的火山引擎API配置 ---
VOLCENGINE_ENDPOINT=https://rtc.volcengineapi.com
VOLCENGINE_REGION=cn-north-1

# --- RTC (实时音视频) 服务 ---
# 在 "实时音视频" -> "应用管理" 中找到
VOLCANO_RTC_APP_ID=67d8035a95a5b2017afa1195
VOLCANO_RTC_APP_KEY=344795c935704b589661b5bd56c72950

# --- ASR (语音识别) 服务 ---
# 在 "语音技术" -> "应用管理" -> "流式语音识别大模型" 中找到
VOLCANO_ASR_APP_ID=8577647692
VOLCANO_ASR_ACCESS_TOKEN=8JoWBhOTffBh0KuRGdD_xJ9cwHJB9Uox

# --- TTS (语音合成) 服务 ---
# 在 "语音技术" -> "应用管理" -> "语音合成大模型" 中找到
VOLCANO_TTS_APP_ID=8577647692
VOLCANO_TTS_ACCESS_TOKEN=8JoWBhOTffBh0KuRGdD_xJ9cwHJB9Uox

# --- LLM (豆包大模型) 服务 ---
# 在 "火山方舟" -> "模型推理" -> "自定义推理" 中创建和获取
VOLCANO_LLM_ENDPOINT_ID=ep-20250704092428-tl9sc
VOLCANO_LLM_APP_KEY=07d67148-88c3-4504-bda5-da35af689f3a

# LiteLLM 火山引擎配置 (兼容性配置)
VOLCENGINE_API_KEY=07d67148-88c3-4504-bda5-da35af689f3a

# --- Webhook 安全验证配置 ---
# Webhook签名验证密钥 (请在生产环境中设置强密钥)
VOLCENGINE_WEBHOOK_SECRET=73a3489D9278fea1c21
# 签名时间容忍度（秒），默认300秒（5分钟）
VOLCENGINE_SIGNATURE_TOLERANCE=300
# 注意：签名验证现已始终启用，移除了可禁用选项以提升安全性
# IP白名单（可选，逗号分隔）- 火山引擎官方IP范围
VOLCENGINE_IP_WHITELIST=
VOLCANO_USE_LICENSE=false

# =============================================================
#  第五部分: 外部记忆服务配置
# =============================================================
# 记忆服务提供商选择: 'zep' 或 'mem0'
MEMORY_PROVIDER=zep

# Mem0 记忆服务配置
MEM0_API_KEY=m0-PvK45GonrdXkluuCLvGVNqXgE0lQvRukJTuOrp5U

# Zep 记忆服务配置
ZEP_API_KEY=z_1dWlkIjoiMjRmY2QwYzktZGQwZC00YTU2LTliNzAtMzU2NTRjN2NkY2NkIn0.4cHnB7CAGrVWyRRfHRoB4Gf1qesJTB95dxOOanrBJL7DY9L58m8zzOXeyjpkfIK3UXmIBb25L4IQbeej_2HbDw

# =============================================================
#  第六部分: 会话分析服务配置
# =============================================================
# 会话后分析任务配置

# 5分钟超时
SESSION_ANALYSIS_TIMEOUT=300
# 最大重试次数
SESSION_ANALYSIS_MAX_RETRIES=3
# 分块token限制
SESSION_ANALYSIS_MAX_TOKENS=4000
# 单段处理大小
SESSION_ANALYSIS_CHUNK_SIZE=3000
# 重试延迟（秒）
SESSION_ANALYSIS_RETRY_DELAY=1
