"""
RTC会话管理路由
实现故事1.4-B要求的RTC会话相关API端点
集成故事1.5要求的会话后分析功能
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from api.models.rtc_models import (
    PrepareSessionRequest, PrepareSessionResponse,
    EndSessionRequest, EndSessionResponse,
    SessionStatusResponse, SessionConfigResponse,
    UpdateSessionCommandRequest, UpdateSessionConfigRequest
)
from api.services.rtc_session_service import get_rtc_session_service, RTCSessionService
from api.services.session_analysis_service import get_session_analysis_service, SessionAnalysisService
from api.settings import logger
from api.dependencies import get_current_user
from typing import Dict, Any

router = APIRouter(
    prefix="/rtc",
    tags=["RTC Sessions"],
)


@router.post("/prepare_session", response_model=PrepareSessionResponse, status_code=status.HTTP_201_CREATED)
async def prepare_rtc_session_route(
    request: PrepareSessionRequest,
    current_user: dict = Depends(get_current_user), # 修复: 添加认证依赖
    rtc_session_service: RTCSessionService = Depends(get_rtc_session_service)
):
    """
    准备RTC会话

    为用户创建一个新的RTC语音会话，包括：
    - 验证用户和角色权限
    - 检查并发会话限制
    - 调用火山引擎StartVoiceChat API
    - 返回RTC连接凭证

    **关键特性：**
    - 3次重试 + 指数退避机制
    - 详细的错误分类和处理
    - 完整的会话状态跟踪
    """
    try:
        # 修复: 从JWT Token中获取用户ID，并创建完整的请求对象
        user_id = current_user.get("sub")
        if not user_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="无效的用户令牌")

        # 创建完整的请求对象，包含从token获取的userId
        from api.models.rtc_models import InternalPrepareSessionRequest
        full_request = InternalPrepareSessionRequest(
            userId=user_id,  # 从JWT Token获取
            sessionId=request.sessionId,
            characterId=request.characterId
        )

        # 调用RTC会话服务准备会话
        result = await rtc_session_service.prepare_session(full_request)

        logger.info(f"RTC会话准备成功: user_id={user_id}, session_id={request.sessionId}")
        return result

    except HTTPException as http_exc:
        logger.error(f"RTC会话准备失败 (HTTP): {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logger.error(f"RTC会话准备异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误，请稍后重试"
        )


@router.post("/end_session", response_model=EndSessionResponse)
async def end_rtc_session_route(
    request: EndSessionRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user), # 修复: 添加认证依赖
    rtc_session_service: RTCSessionService = Depends(get_rtc_session_service),
    session_analysis_service: SessionAnalysisService = Depends(get_session_analysis_service)
):
    """
    结束RTC会话并触发异步会话后分析

    优雅地终止一个活跃的RTC会话，包括：
    - 验证会话所有权
    - 调用火山引擎StopVoiceChat API
    - 更新会话状态为已结束
    - 清理相关资源
    - **[故事1.5新增]** 触发异步会话后分析任务

    **异步处理机制：**
    - 会话结束立即返回，不阻塞用户
    - 后台执行摘要生成和记忆服务同步
    - 实现重试机制和错误处理
    """
    try:
        # 修复: 从JWT Token中获取用户ID
        user_id = current_user.get("sub")
        if not user_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="无效的用户令牌")

        logger.info(f"收到RTC会话结束请求 - SessionId: {request.sessionId}, TaskId: {request.taskId}")

        # 1. 立即停止RTC会话
        success = await rtc_session_service.end_session(
            session_id=request.sessionId,
            user_id=user_id,  # 使用从JWT Token获取的用户ID
            task_id=request.taskId
        )

        # 2. 触发异步会话后分析任务（故事1.5核心功能）
        if success:
            logger.info(f"启动会话后分析任务 - SessionId: {request.sessionId}")
            background_tasks.add_task(
                session_analysis_service.analyze_session_and_sync_memory,
                session_id=request.sessionId
            )

        response = EndSessionResponse(
            success=success,
            message="会话已成功结束，分析任务已启动" if success else "会话结束可能未完全成功，但状态已更新"
        )

        logger.info(f"RTC会话结束完成 - SessionId: {request.sessionId}, Success: {success}")
        return response

    except HTTPException as http_exc:
        raise http_exc

    except Exception as e:
        logger.error(f"RTC会话结束发生未知错误 - SessionId: {request.sessionId}, Error: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="结束会话时发生错误"
        )


@router.get("/sessions/{session_id}/status", response_model=SessionStatusResponse)
async def get_session_status_route(
    session_id: str,
    current_user: dict = Depends(get_current_user), # 修复: 添加认证依赖
    rtc_session_service: RTCSessionService = Depends(get_rtc_session_service)
):
    """
    获取会话状态

    查询指定RTC会话的当前状态信息，包括：
    - 会话状态（preparing|active|ended|error）
    - 开始和结束时间
    - 关联的任务ID

    **状态说明：**
    - preparing: 会话正在准备中
    - active: 会话已激活，用户可加入
    - ended: 会话已正常结束
    - error: 会话出现错误
    """
    try:
        # 修复: 从JWT Token中获取用户ID
        user_id = current_user.get("sub")
        if not user_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="无效的用户令牌")

        logger.info(f"获取会话状态 - SessionId: {session_id}, UserId: {user_id}")

        response = await rtc_session_service.get_session_status(session_id, user_id)

        logger.debug(f"会话状态查询成功 - SessionId: {session_id}, Status: {response.status}")
        return response

    except HTTPException as http_exc:
        raise http_exc

    except Exception as e:
        logger.error(f"获取会话状态发生错误 - SessionId: {session_id}, Error: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="查询会话状态时发生错误"
        )


@router.get("/sessions/{session_id}/config", response_model=SessionConfigResponse)
async def get_session_config_route(
    session_id: str,
    current_user: dict = Depends(get_current_user), # 修复: 添加认证依赖
    rtc_session_service: RTCSessionService = Depends(get_rtc_session_service)
):
    """
    获取会话配置

    获取指定RTC会话的配置信息，包括：
    - LLM模型配置
    - 语音合成配置
    - AI角色配置

    **安全说明：**
    - 敏感信息（如API密钥）已自动脱敏
    - 仅返回用户有权访问的配置项
    """
    try:
        # 修复: 从JWT Token中获取用户ID
        user_id = current_user.get("sub")
        if not user_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="无效的用户令牌")

        logger.info(f"获取会话配置 - SessionId: {session_id}, UserId: {user_id}")

        response = await rtc_session_service.get_session_config(session_id, user_id)

        logger.debug(f"会话配置查询成功 - SessionId: {session_id}")
        return response

    except HTTPException as http_exc:
        raise http_exc

    except Exception as e:
        logger.error(f"获取会话配置发生错误 - SessionId: {session_id}, Error: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="查询会话配置时发生错误"
        )


@router.put("/sessions/{session_id}/config", response_model=SessionConfigResponse)
async def update_session_config_route(
    session_id: str,
    request: UpdateSessionConfigRequest,
    current_user: dict = Depends(get_current_user), # 修复: 添加认证依赖
    rtc_session_service: RTCSessionService = Depends(get_rtc_session_service)
):
    """
    更新会话配置

    动态更新活跃RTC会话的配置，支持：
    - 语音合成参数调整（语速、音调等）
    - LLM模型参数调整（温度、最大令牌数等）

    **注意事项：**
    - 配置更改会实时生效
    - 配置变更会记录版本历史
    - 某些配置可能需要重启会话才能生效
    """
    try:
        # 修复: 从JWT Token中获取用户ID
        user_id = current_user.get("sub")
        if not user_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="无效的用户令牌")

        logger.info(f"更新会话配置 - SessionId: {session_id}, UserId: {user_id}")

        # TODO: 实现配置更新逻辑
        # 目前返回当前配置作为占位符
        current_config = await rtc_session_service.get_session_config(session_id, user_id)

        logger.info(f"会话配置更新成功 - SessionId: {session_id}")
        return current_config

    except HTTPException as http_exc:
        raise http_exc

    except Exception as e:
        logger.error(f"更新会话配置发生错误 - SessionId: {session_id}, Error: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新会话配置时发生错误"
        )


@router.post("/sessions/{session_id}/command",
             summary="向RTC会话发送命令",
             response_model=Dict[str, bool])
async def send_command_to_session(
    session_id: str,
    command_request: UpdateSessionCommandRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    rtc_session_service: RTCSessionService = Depends(get_rtc_session_service)
):
    """
    向一个正在进行的RTC会话发送控制命令，如手动中断。
    """
    # 修复: 从JWT Token的sub字段获取用户ID
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials: missing user ID"
        )

    success = await rtc_session_service.send_session_command(
        session_id=session_id,
        user_id=user_id,
        command=command_request.command,
        message=command_request.message,
        interrupt_mode=command_request.interrupt_mode
    )
    if not success:
        raise HTTPException(
            status_code=500,
            detail="发送命令失败"
        )
    return {"success": True}
