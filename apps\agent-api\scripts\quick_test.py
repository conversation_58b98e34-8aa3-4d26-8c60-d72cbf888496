#!/usr/bin/env python3
"""
快速测试脚本 - 验证修复后的会话持久性
========================================

测试内容：
1. 验证本地记忆存储（JSON格式）
2. 验证Zep集成
3. 验证会话持久性
4. 验证记忆恢复

使用方法：
python quick_test.py
"""

import asyncio
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from advanced_memory_agent import XinqiaoAgent, AgentConfig

async def quick_test():
    """快速测试"""
    print("🧪 快速修复验证测试")
    print("=" * 40)

    # 清理之前的测试数据
    import shutil
    if Path("session_info.json").exists():
        Path("session_info.json").unlink()
    if Path("memory_db").exists():
        shutil.rmtree("memory_db")

    # 创建配置
    config = AgentConfig.from_env()
    print(f"👤 用户ID: {config.user_id}")
    print(f"💬 会话ID: {config.session_id}")

    # 创建智能体
    agent = XinqiaoAgent(config)

    # 初始化
    if not await agent.initialize():
        print("❌ 智能体初始化失败")
        return

    print("✅ 智能体初始化成功")

    # 测试记忆添加
    print("\n📝 测试记忆添加...")
    response1 = await agent.chat("我叫Alex Hu，请记住我的名字")
    print(f"回复: {response1[:50]}...")

    # 等待记忆处理
    await asyncio.sleep(2)

    # 检查记忆统计
    stats = await agent.get_memory_stats()
    print(f"📊 记忆统计: {stats['total_memories']} 条记忆")

    # 清理第一个智能体
    await agent.cleanup()
    print("🔄 模拟程序重启...")

    # 重新创建智能体（模拟重启）
    agent2 = XinqiaoAgent(config)
    if not await agent2.initialize():
        print("❌ 第二次初始化失败")
        return

    print("✅ 第二次初始化成功")

    # 测试记忆恢复
    print("\n🔍 测试记忆恢复...")
    response2 = await agent2.chat("你还记得我的名字吗？")
    print(f"回复: {response2}")

    # 检查是否包含正确信息
    if "Alex" in response2 and "Hu" in response2:
        print("✅ 记忆恢复成功")
    else:
        print("❌ 记忆恢复失败")

    # 检查JSON文件
    memory_files = list(Path("memory_db").glob("*.json"))
    print(f"📁 JSON记忆文件: {len(memory_files)} 个")

    if memory_files:
        print("✅ JSON存储正常")
    else:
        print("❌ JSON存储失败")

    # 清理
    await agent2.cleanup()
    print("✅ 测试完成")

async def main():
    """主函数"""
    try:
        await quick_test()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
